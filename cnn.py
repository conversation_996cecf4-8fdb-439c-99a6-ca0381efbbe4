import torch
from torch import nn
from torchvision import ops
from torchvision.models.video import mc3_18, MC3_18_Weights


class Classifier(nn.Module):
    def __init__(self):
        super().__init__()

        # Load pretrained MC3_18 model
        self.backbone = mc3_18(weights=MC3_18_Weights.KINETICS400_V1)
        self.backbone.fc = nn.Identity()  # Remove final classification layer

        # MLP expects a 2D input, so we'll do global average pooling manually
        self.global_pool = nn.AdaptiveAvgPool3d((1, 1, 1))  # Output shape: [B, 512, 1, 1, 1]

        # MLP classification head
        self.mlp = ops.MLP(in_channels=512, hidden_channels=[2048, 512],
                           dropout=0.25, activation_layer=nn.ReLU)

        self.linear1 = nn.Linear(512, 64)
        self.linear2 = nn.Linear(64, 1)

    def forward(self, x):
        # Feature extraction from backbone
        features = self.backbone.stem(x)
        features = self.backbone.layer1(features)
        features = self.backbone.layer2(features)
        features = self.backbone.layer3(features)
        features = self.backbone.layer4(features)

        # Global average pooling: [B, 512, T, H, W] → [B, 512, 1, 1, 1]
        features = self.global_pool(features)

        # Flatten to [B, 512]
        features = torch.flatten(features, 1)

        # Pass through MLP and final linear layers
        features = self.mlp(features)
        features = self.linear1(features)
        return self.linear2(features)



