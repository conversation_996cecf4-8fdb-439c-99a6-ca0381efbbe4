from ultralytics import YOLO
import os

print("Testing ONNX model loading...")

# Check if ONNX file exists
onnx_path = "data/models/yolo11s.onnx"
print(f"ONNX file exists: {os.path.exists(onnx_path)}")

if os.path.exists(onnx_path):
    print("Loading ONNX model...")
    try:
        onnx_model = YOLO(onnx_path, task='detect')
        print("✓ ONNX model loaded successfully")
        
        # Test inference
        print("Running inference...")
        results = onnx_model.predict("https://ultralytics.com/images/bus.jpg", verbose=False)
        print(f"✓ Inference successful - detected {len(results[0].boxes)} objects")
        
    except Exception as e:
        print(f"✗ Error loading ONNX model: {e}")
else:
    print("ONNX file not found")
