"""
Detection utilities for YOLO person detection and DeepSORT tracking
"""

import cv2
import numpy as np
from typing import List, Tuple

# DeepSORT tracking (optional - falls back to YOLO tracking if not available)
try:
    from deep_sort_realtime import DeepSort
    DEEPSORT_AVAILABLE = True
except ImportError:
    DEEPSORT_AVAILABLE = False
    print("⚠️  DeepSORT not available, using YOLO built-in tracking")


# Global DeepSORT tracker instance
_deepsort_tracker = None

def get_deepsort_tracker():
    """Get or create DeepSORT tracker instance"""
    global _deepsort_tracker
    if _deepsort_tracker is None and DEEPSORT_AVAILABLE:
        _deepsort_tracker = DeepSort(max_age=50, n_init=3)
    return _deepsort_tracker

def detect_persons(yolo_model, frame, confidence_threshold=0.1, device_index=None, use_deepsort=True):
    """
    Detect persons in frame using YOLO model with optional DeepSORT tracking

    Args:
        yolo_model: YOLO model instance
        frame: Input frame
        confidence_threshold: Minimum confidence for detection
        device_index: GPU device index (None for CPU, ignored for TensorRT models)
        use_deepsort: Whether to use DeepSORT tracking (falls back to YOLO tracking if unavailable)

    Returns:
        Tuple of (boxes, confidences, ids)
    """
    # Check if this is a TensorRT model using stored information
    is_tensorrt = getattr(yolo_model, '_is_tensorrt', False)

    # First, get detections from YOLO (without tracking for DeepSORT)
    if use_deepsort and DEEPSORT_AVAILABLE:
        # Use YOLO for detection only (no tracking)
        if is_tensorrt:
            results = yolo_model.predict(
                frame,
                classes=[0],  # Person class
                verbose=False
            )[0]
        else:
            results = yolo_model.predict(
                frame,
                classes=[0],  # Person class
                verbose=False,
                device=device_index
            )[0]

        # Extract detections for DeepSORT
        detections = []
        if results.boxes is not None:
            for det in results.boxes:
                conf = float(det.conf)
                if conf > confidence_threshold:
                    x1, y1, x2, y2 = map(int, det.xyxy[0])
                    detections.append(([x1, y1, x2 - x1, y2 - y1], conf, 'person'))

        # Use DeepSORT for tracking
        tracker = get_deepsort_tracker()
        if tracker is not None:
            tracks = tracker.update_tracks(detections, frame=frame)

            boxes, confidences, ids = [], [], []
            for track in tracks:
                if not track.is_confirmed():
                    continue

                track_id = track.track_id
                ltrb = track.to_ltrb()
                x1, y1, x2, y2 = map(int, ltrb)

                # Find confidence for this detection
                conf = confidence_threshold  # Default confidence
                for det_bbox, det_conf, _ in detections:
                    det_x1, det_y1, det_w, det_h = det_bbox
                    det_x2, det_y2 = det_x1 + det_w, det_y1 + det_h

                    # Check if this detection matches the track (simple overlap check)
                    if (abs(x1 - det_x1) < 20 and abs(y1 - det_y1) < 20 and
                        abs(x2 - det_x2) < 20 and abs(y2 - det_y2) < 20):
                        conf = det_conf
                        break

                boxes.append((x1, y1, x2, y2))
                confidences.append(conf)
                ids.append(track_id)

            return boxes, confidences, ids

    # Fallback to YOLO built-in tracking
    if is_tensorrt:
        results = yolo_model.predict(
            frame,
            classes=[0],  # Person class
            verbose=False
        )[0]
    else:
        results = yolo_model.track(
            frame,
            persist=True,
            classes=[0],  # Person class
            verbose=False,
            device=device_index
        )[0]

    boxes, confidences, ids = [], [], []

    if results.boxes is not None:
        for det in results.boxes:
            conf = float(det.conf)
            track_id = det.id

            if conf > confidence_threshold and track_id is not None:
                x1, y1, x2, y2 = map(int, det.xyxy[0])
                boxes.append((x1, y1, x2, y2))
                confidences.append(conf)
                ids.append(int(track_id))

    return boxes, confidences, ids


def reset_tracker(yolo_model=None):
    """
    Reset both YOLO and DeepSORT trackers to start fresh tracking

    Args:
        yolo_model: YOLO model instance (optional)
    """
    reset_success = False

    # Reset DeepSORT tracker
    global _deepsort_tracker
    if _deepsort_tracker is not None:
        _deepsort_tracker = None  # Reset by recreating
        print("🔄 DeepSORT tracker reset")
        reset_success = True

    # Reset YOLO tracker
    if yolo_model is not None:
        try:
            if hasattr(yolo_model, 'predictor') and yolo_model.predictor is not None:
                if hasattr(yolo_model.predictor, 'trackers') and len(yolo_model.predictor.trackers) > 0:
                    yolo_model.predictor.trackers[0].reset()
                    print("🔄 YOLO tracker reset")
                    reset_success = True
        except Exception as e:
            print(f"Warning: Could not reset YOLO tracker: {e}")

    return reset_success

def reset_yolo_tracker(yolo_model):
    """
    Reset YOLO tracker to start fresh tracking (legacy function)

    Args:
        yolo_model: YOLO model instance
    """
    return reset_tracker(yolo_model)


def get_tracker_info(yolo_model):
    """
    Get information about the current YOLO tracker state
    
    Args:
        yolo_model: YOLO model instance
    
    Returns:
        String describing tracker state
    """
    try:
        if hasattr(yolo_model, 'predictor') and yolo_model.predictor is not None:
            if hasattr(yolo_model.predictor, 'trackers') and len(yolo_model.predictor.trackers) > 0:
                tracker = yolo_model.predictor.trackers[0]
                if hasattr(tracker, 'tracks'):
                    active_tracks = len([t for t in tracker.tracks if t.is_confirmed()])
                    return f"Active tracks: {active_tracks}"
        return "Tracker not initialized"
    except Exception as e:
        return f"Tracker error: {e}"


def initialize_yolo_tracker(yolo_model, frame_shape=(480, 640, 3)):
    """
    Initialize YOLO tracker with a dummy frame
    
    Args:
        yolo_model: YOLO model instance
        frame_shape: Shape of dummy frame (height, width, channels)
    """
    dummy_frame = np.zeros(frame_shape, dtype=np.uint8)
    _ = yolo_model.track(dummy_frame, persist=True, classes=[0], verbose=False)
    reset_yolo_tracker(yolo_model)
