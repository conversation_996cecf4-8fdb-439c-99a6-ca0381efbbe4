from ultralytics import YOLO

# Load the YOLO11 model (using PyTorch model instead of TensorRT)
model = YOLO("data/models/yolo11s.pt")

# Export the model to TensorRT format (commented out - requires TensorRT installation)
#model.export(format="engine",task="detect",device=0,nms=True)  # creates 'yolo11n.engine'

# Load the exported TensorRT model (commented out - requires TensorRT installation)
#tensorrt_model = YOLO("data/models/yolo11s.engine",task='detect')

# Run inference with PyTorch model
results = model.predict("https://ultralytics.com/images/bus.jpg")

print(results[0].boxes.xyxy)

# Print the results
print(results)