import cv2
import time
import argparse
import numpy as np
from ultralytics import YOL<PERSON>

from utils.models import load_yolo_model, get_device
from utils.detection import detect_persons

def main(model_path: str, video_path: str):
    """
    Main function to run YOLO inference on a video and measure performance.

    Args:
        model_path (str): Path to the TensorRT YOLO model (.engine file).
        video_path (str): Path to the input video file.
    """
    # 1. Setup
    device = get_device(prefer_gpu=True)
    
    # 2. Load YOLO model
    print(f"Loading model from: {model_path}")
    yolo_model = load_yolo_model(model_path, device)
    if yolo_model is None:
        print("Failed to load YOLO model. Exiting.")
        return

    # 3. Open video capture
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"Error: Could not open video file {video_path}")
        return

    frame_count = 0
    total_inference_time = 0
    
    # 4. Warm-up run (optional but recommended for stable measurements)
    print("Warming up the model...")
    ret, frame = cap.read()
    if ret:
        _ = detect_persons(yolo_model, frame)
        print("Warm-up complete.")
    else:
        print("Could not read the first frame for warm-up.")
        cap.release()
        return
        
    # Reset video to the beginning
    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)

    # 5. Process video
    print("Starting video processing...")
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame_count += 1

        # Run detection and measure time
        start_time = time.perf_counter()
        boxes, confidences, ids = detect_persons(yolo_model, frame)
        end_time = time.perf_counter()
        
        inference_time = (end_time - start_time) * 1000  # in milliseconds
        total_inference_time += inference_time

        # Optional: Display the frame with detections
        # for box, conf, id_ in zip(boxes, confidences, ids):
        #     x1, y1, x2, y2 = box
        #     cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        #     label = f"ID: {id_} Conf: {conf:.2f}"
        #     cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        #
        # cv2.imshow('YOLO Inference', frame)
        # if cv2.waitKey(1) & 0xFF == ord('q'):
        #     break
            
        if frame_count % 30 == 0: # Print progress every 30 frames
            print(f"Processed {frame_count} frames...")


    # 6. Clean up and report results
    cap.release()
    cv2.destroyAllWindows()

    if frame_count > 0:
        avg_inference_time = total_inference_time / frame_count
        print("\n--- Inference Performance ---")
        print(f"Processed {frame_count} frames.")
        print(f"Average inference time per frame: {avg_inference_time:.2f} ms")
        print(f"Average FPS: {1000 / avg_inference_time:.2f}")
        print("---------------------------")
    else:
        print("No frames were processed from the video.")


if __name__ == "__main__":
    # --- USER-CONFIGURABLE PATHS ---
    MODEL_PATH = "data/models/yolo11s.engine"  # <<< CHANGE THIS
    VIDEO_PATH = "data/feed/Untitled Project.mp4"      # <<< CHANGE THIS
    # --------------------------------

    if MODEL_PATH == "path/to/your/model.engine" or VIDEO_PATH == "path/to/your/video.mp4":
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print("!!! PLEASE UPDATE THE MODEL_PATH AND VIDEO_PATH IN   !!!")
        print("!!! test.py BEFORE RUNNING THE SCRIPT.               !!!")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    else:
        main(MODEL_PATH, VIDEO_PATH)