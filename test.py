from ultralytics import YOLO

# Load the YOLO11 model
#model = YOLO("data/models/yolo11s.pt")

# Export the model to TensorRT format
#model.export(format="engine",task="detect",device=0,nms=True)  # creates 'yolo11n.engine'

# Load the exported TensorRT model
tensorrt_model = YOLO("data/models/yolo11s.engine")

# Run inference
results = tensorrt_model.predict("https://ultralytics.com/images/bus.jpg")

# Print the results
print(results)