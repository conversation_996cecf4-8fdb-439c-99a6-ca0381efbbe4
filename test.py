import torch_tensorrt
import torch
import cnn

model = cnn.Classifier().cuda()
# Example input
inputs = torch.randn(1, 3, 16, 112, 112).cuda()

# Compile
trt_model = torch_tensorrt.compile(
    model,
    ir="dynamo",
    inputs=[torch_tensorrt.Input(inputs.shape, dtype=torch.float32)],
)

# Save with example input for tracing
torch_tensorrt.save(
    trt_model,
    "data/models/trained_model_cnn_trt.ts",
    output_format="torchscript",
    inputs=[inputs]  # ← you must include this!
)
