from ultralytics import YOLO
import time
import os

print("="*60)
print("YOLO MODEL PERFORMANCE COMPARISON")
print("="*60)

# Load the YOLO11 PyTorch model
print("\n1. Loading PyTorch model...")
pytorch_model = YOLO("data/models/yolo11s.pt")
print("   ✓ PyTorch model loaded")

# Check if ONNX model exists, if not export it
onnx_path = "data/models/yolo11s.onnx"
print("\n2. Loading ONNX model...")
if not os.path.exists(onnx_path):
    print("   ! ONNX model not found, exporting...")
    pytorch_model.export(format="onnx", task="detect", device=0, nms=True)
    print(f"   ✓ ONNX model exported to {onnx_path}")
else:
    print("   ✓ ONNX model loaded from existing file")

# Load the ONNX model
onnx_model = YOLO(onnx_path, task='detect')

# Test image
test_image = "https://ultralytics.com/images/bus.jpg"

print("\n3. Warming up models...")
# Warm up both models (first inference is usually slower)
_ = pytorch_model.predict(test_image, verbose=False)
_ = onnx_model.predict(test_image, verbose=False)
print("   ✓ Models warmed up")

print("\n" + "="*60)
print("PERFORMANCE TESTING")
print("="*60)

# Test PyTorch model performance (multiple runs for accuracy)
print("\n--- PyTorch Model Performance ---")
pytorch_times = []
for i in range(3):
    start_time = time.time()
    pytorch_results = pytorch_model.predict(test_image, verbose=False)
    pytorch_time = time.time() - start_time
    pytorch_times.append(pytorch_time)
    print(f"   Run {i+1}: {pytorch_time:.4f}s")

pytorch_avg = sum(pytorch_times) / len(pytorch_times)
print(f"   Average: {pytorch_avg:.4f}s")

# Test ONNX model performance (multiple runs for accuracy)
print("\n--- ONNX Model Performance ---")
onnx_times = []
for i in range(3):
    start_time = time.time()
    onnx_results = onnx_model.predict(test_image, verbose=False)
    onnx_time = time.time() - start_time
    onnx_times.append(onnx_time)
    print(f"   Run {i+1}: {onnx_time:.4f}s")

onnx_avg = sum(onnx_times) / len(onnx_times)
print(f"   Average: {onnx_avg:.4f}s")

# Performance comparison
print("\n" + "="*60)
print("RESULTS SUMMARY")
print("="*60)
print(f"PyTorch average time: {pytorch_avg:.4f}s")
print(f"ONNX average time:    {onnx_avg:.4f}s")

if pytorch_avg > onnx_avg:
    speedup = pytorch_avg / onnx_avg
    print(f"🚀 ONNX is {speedup:.2f}x faster than PyTorch")
else:
    slowdown = onnx_avg / pytorch_avg
    print(f"⚡ PyTorch is {slowdown:.2f}x faster than ONNX")

# Verify detection accuracy
print(f"\nDetection verification:")
print(f"PyTorch detections: {len(pytorch_results[0].boxes)} objects")
print(f"ONNX detections:    {len(onnx_results[0].boxes)} objects")

if len(pytorch_results[0].boxes) == len(onnx_results[0].boxes):
    print("✓ Both models detected the same number of objects")
else:
    print("⚠ Different number of detections between models")

print(f"\nDetected objects: {pytorch_results[0].boxes.cls.unique().cpu().numpy()}")
print("\nBounding boxes (ONNX model):")
print(onnx_results[0].boxes.xyxy)
print("="*60)