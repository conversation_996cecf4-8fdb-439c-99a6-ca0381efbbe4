from ultralytics import YOLO
import time
import os

# Check TensorRT availability and version
try:
    import tensorrt as trt
    # Try different ways to get version
    if hasattr(trt, '__version__'):
        trt_version = trt.__version__
    elif hasattr(trt, 'version'):
        trt_version = trt.version
    else:
        trt_version = "Unknown (TensorRT available)"
    print(f"TensorRT version: {trt_version}")
    tensorrt_available = True
except ImportError:
    print("TensorRT not available")
    tensorrt_available = False
except Exception as e:
    print(f"TensorRT import error: {e}")
    tensorrt_available = False


print("="*60)
print("YOLO MODEL PERFORMANCE COMPARISON")
print("="*60)

# Load the YOLO11 PyTorch model
print("\n1. Loading PyTorch model...")
pytorch_model = YOLO("data/models/yolo11s.pt")
print("   ✓ PyTorch model loaded")

# Check if ONNX model exists, if not export it
onnx_path = "data/models/yolo11s.onnx"
print("\n2. Loading ONNX model...")
if not os.path.exists(onnx_path):
    print("   ! ONNX model not found, exporting...")
    pytorch_model.export(format="onnx", task="detect", device=0, nms=True)
    print(f"   ✓ ONNX model exported to {onnx_path}")
else:
    print("   ✓ ONNX model loaded from existing file")

# Load the ONNX model
onnx_model = YOLO(onnx_path, task='detect')

# Check if TensorRT model exists, if not export it
tensorrt_path = "data/models/yolo11s.engine"
tensorrt_model = None
print("\n3. Loading TensorRT model...")

if not tensorrt_available:
    print("   ✗ TensorRT not available, skipping TensorRT model")
else:
    try:
        if not os.path.exists(tensorrt_path):
            print("   ! TensorRT model not found, exporting...")
            print("   ⏳ This may take several minutes...")
            pytorch_model.export(format="engine", task="detect", device=0, nms=True)
            print(f"   ✓ TensorRT model exported to {tensorrt_path}")
        else:
            print("   ✓ TensorRT model found, loading...")

        # Load the TensorRT model
        tensorrt_model = YOLO(tensorrt_path, task='detect')
        print("   ✓ TensorRT model loaded successfully")

    except Exception as e:
        print(f"   ✗ TensorRT model failed to load: {str(e)}")
        print("   ℹ TensorRT comparison will be skipped")
        tensorrt_model = None

# Test image
test_image = "https://ultralytics.com/images/bus.jpg"
test_image = "data/feed/Untitled Project.mp4"

print(f"\n4. Warming up models...")
# Warm up models (first inference is usually slower)
_ = pytorch_model.predict(test_image, verbose=False)
_ = onnx_model.predict(test_image, verbose=False)
if tensorrt_model:
    _ = tensorrt_model.predict(test_image, verbose=False)
print("   ✓ Models warmed up")

print("\n" + "="*60)
print("PERFORMANCE TESTING")
print("="*60)

# Test PyTorch model performance (multiple runs for accuracy)
print("\n--- PyTorch Model Performance ---")
pytorch_times = []
for i in range(3):
    start_time = time.time()
    pytorch_results = pytorch_model.predict(test_image, verbose=False)
    pytorch_time = time.time() - start_time
    pytorch_times.append(pytorch_time)
    print(f"   Run {i+1}: {pytorch_time:.4f}s")

pytorch_avg = sum(pytorch_times) / len(pytorch_times)
print(f"   Average: {pytorch_avg:.4f}s")

# Test ONNX model performance (multiple runs for accuracy)
print("\n--- ONNX Model Performance ---")
onnx_times = []
for i in range(3):
    start_time = time.time()
    onnx_results = onnx_model.predict(test_image, verbose=False)
    onnx_time = time.time() - start_time
    onnx_times.append(onnx_time)
    print(f"   Run {i+1}: {onnx_time:.4f}s")

onnx_avg = sum(onnx_times) / len(onnx_times)
print(f"   Average: {onnx_avg:.4f}s")

# Test TensorRT model performance (if available)
tensorrt_avg = None
if tensorrt_model:
    print("\n--- TensorRT Model Performance ---")
    tensorrt_times = []
    for i in range(3):
        start_time = time.time()
        tensorrt_results = tensorrt_model.predict(test_image, verbose=False)
        tensorrt_time = time.time() - start_time
        tensorrt_times.append(tensorrt_time)
        print(f"   Run {i+1}: {tensorrt_time:.4f}s")

    tensorrt_avg = sum(tensorrt_times) / len(tensorrt_times)
    print(f"   Average: {tensorrt_avg:.4f}s")

# Performance comparison
print("\n" + "="*60)
print("RESULTS SUMMARY")
print("="*60)
print(f"PyTorch average time:  {pytorch_avg:.4f}s")
print(f"ONNX average time:     {onnx_avg:.4f}s")
if tensorrt_avg:
    print(f"TensorRT average time: {tensorrt_avg:.4f}s")

print("\nPerformance Improvements:")
# ONNX vs PyTorch
if pytorch_avg > onnx_avg:
    speedup = pytorch_avg / onnx_avg
    print(f"🚀 ONNX is {speedup:.2f}x faster than PyTorch")
else:
    slowdown = onnx_avg / pytorch_avg
    print(f"⚡ PyTorch is {slowdown:.2f}x faster than ONNX")

# TensorRT vs PyTorch
if tensorrt_avg:
    if pytorch_avg > tensorrt_avg:
        speedup = pytorch_avg / tensorrt_avg
        print(f"🚀 TensorRT is {speedup:.2f}x faster than PyTorch")
    else:
        slowdown = tensorrt_avg / pytorch_avg
        print(f"⚡ PyTorch is {slowdown:.2f}x faster than TensorRT")

    # TensorRT vs ONNX
    if onnx_avg > tensorrt_avg:
        speedup = onnx_avg / tensorrt_avg
        print(f"🚀 TensorRT is {speedup:.2f}x faster than ONNX")
    else:
        slowdown = tensorrt_avg / onnx_avg
        print(f"⚡ ONNX is {slowdown:.2f}x faster than TensorRT")

# Verify detection accuracy
print(f"\nDetection verification:")
print(f"PyTorch detections:  {len(pytorch_results[0].boxes)} objects")
print(f"ONNX detections:     {len(onnx_results[0].boxes)} objects")
if tensorrt_avg:
    print(f"TensorRT detections: {len(tensorrt_results[0].boxes)} objects")

# Check consistency
all_same = len(pytorch_results[0].boxes) == len(onnx_results[0].boxes)
if tensorrt_avg:
    all_same = all_same and len(pytorch_results[0].boxes) == len(tensorrt_results[0].boxes)

if all_same:
    print("✓ All models detected the same number of objects")
else:
    print("⚠ Different number of detections between models")

print(f"\nDetected objects: {pytorch_results[0].boxes.cls.unique().cpu().numpy()}")
print("="*60)